import { useCallback } from "react";
import { useTranslation } from "react-i18next";
import {
  PlayIcon,
  DownloadIcon,
  HeartIcon,
  HeartFillIcon,
  GearIcon,
  PlusIcon,
} from "@primer/octicons-react";

import type { LibraryGame } from "@types";

import "./library-quick-actions.scss";

interface LibraryQuickActionsProps {
  game: LibraryGame;
  onPlay?: (game: LibraryGame) => void;
  onDownload?: (game: LibraryGame) => void;
  onToggleFavorite?: (game: LibraryGame) => void;
  onShowOptions?: (game: LibraryGame) => void;
  onAddToCollection?: (game: LibraryGame) => void;
  className?: string;
}

export function LibraryQuickActions({
  game,
  onPlay,
  onDownload,
  onToggleFavorite,
  onShowOptions,
  onAddToCollection,
  className = "",
}: LibraryQuickActionsProps) {
  const { t } = useTranslation("library");

  const handlePlay = useCallback(() => {
    onPlay?.(game);
  }, [onPlay, game]);

  const handleDownload = useCallback(() => {
    onDownload?.(game);
  }, [onDownload, game]);

  const handleToggleFavorite = useCallback(() => {
    onToggleFavorite?.(game);
  }, [onToggleFavorite, game]);

  const handleShowOptions = useCallback(() => {
    onShowOptions?.(game);
  }, [onShowOptions, game]);

  const handleAddToCollection = useCallback(() => {
    onAddToCollection?.(game);
  }, [onAddToCollection, game]);

  const isInstalled = Boolean(game.executablePath);
  const isFavorite = Boolean(game.isFavorite);

  return (
    <div className={`library-quick-actions ${className}`}>
      {/* Primary Action - Play or Download */}
      {isInstalled ? (
        <button
          type="button"
          className="library-quick-actions__button library-quick-actions__button--primary"
          onClick={handlePlay}
          title={t("play_game")}
        >
          <PlayIcon size={16} />
          <span>{t("play")}</span>
        </button>
      ) : (
        <button
          type="button"
          className="library-quick-actions__button library-quick-actions__button--primary"
          onClick={handleDownload}
          title={t("download_game")}
        >
          <DownloadIcon size={16} />
          <span>{t("download")}</span>
        </button>
      )}

      {/* Secondary Actions */}
      <div className="library-quick-actions__secondary">
        {/* Favorite Toggle */}
        <button
          type="button"
          className={`library-quick-actions__button library-quick-actions__button--icon ${
            isFavorite ? "library-quick-actions__button--active" : ""
          }`}
          onClick={handleToggleFavorite}
          title={isFavorite ? t("remove_from_favorites") : t("add_to_favorites")}
        >
          {isFavorite ? <HeartFillIcon size={16} /> : <HeartIcon size={16} />}
        </button>

        {/* Add to Collection */}
        <button
          type="button"
          className="library-quick-actions__button library-quick-actions__button--icon"
          onClick={handleAddToCollection}
          title={t("add_to_collection")}
        >
          <PlusIcon size={16} />
        </button>

        {/* Game Options */}
        <button
          type="button"
          className="library-quick-actions__button library-quick-actions__button--icon"
          onClick={handleShowOptions}
          title={t("game_options")}
        >
          <GearIcon size={16} />
        </button>
      </div>
    </div>
  );
}
